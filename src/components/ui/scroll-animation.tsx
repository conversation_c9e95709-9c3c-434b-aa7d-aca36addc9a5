"use client";

import { useRef } from "react";
import * as motion from "motion/react-client";

interface ScrollAnimationProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  direction?: "up" | "down" | "left" | "right";
  distance?: number;
  threshold?: number;
  duration?: number;
  once?: boolean;
}

export function ScrollAnimation({
  children,
  className,
  delay = 0,
  direction = "up",
  distance = 50,
  threshold = 0.1,
  duration = 0.5,
  once = true,
}: ScrollAnimationProps) {
  const ref = useRef(null);

  // 根据方向设置初始和最终位置
  const getInitialPosition = () => {
    switch (direction) {
      case "up":
        return { opacity: 0, y: distance };
      case "down":
        return { opacity: 0, y: -distance };
      case "left":
        return { opacity: 0, x: distance };
      case "right":
        return { opacity: 0, x: -distance };
      default:
        return { opacity: 0, y: distance };
    }
  };

  const getFinalPosition = () => {
    return { opacity: 1, y: 0, x: 0 };
  };

  return (
    <motion.div
      ref={ref}
      className={className}
      initial={getInitialPosition()}
      whileInView={getFinalPosition()}
      viewport={{ once, threshold }}
      transition={{ duration, delay, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  );
}
