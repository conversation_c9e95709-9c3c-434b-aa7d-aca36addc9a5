import { Navbar } from "./components/Navbar";
import { HeroSection } from "./components/HeroSection";
import { FeaturesSection } from "./components/FeaturesSection";
import { GallerySection } from "./components/GallerySection";
import { ModelSection } from "./components/ModelSection";
import { PricingSection } from "./components/PricingSection";
import { CtaSection } from "./components/CtaSection";
import { Footer } from "./components/Footer";
import { auth } from "@/server/auth";
import { BackgroundBlurs } from "./components/BackgroundBlurs";

export default async function Home() {
  const session = null;

  return (
    <main className="min-h-screen bg-gradient-to-b from-indigo-950 via-purple-950 to-indigo-950">
      <BackgroundBlurs />
      
      <Navbar session={session} />

      <div className="container relative z-10 mx-auto px-4 sm:px-6">
        <div className="pt-16">
          <HeroSection />
          <ModelSection />
          <FeaturesSection />
          <PricingSection />
          <GallerySection />
          <CtaSection />
        </div>
      </div>

      <Footer />
    </main>
  );
}
