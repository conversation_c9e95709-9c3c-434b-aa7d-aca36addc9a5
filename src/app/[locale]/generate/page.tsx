"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { motion } from "motion/react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { BackgroundBlurs } from "../components/BackgroundBlurs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";

export default function Generate() {
  const t = useTranslations("generate");
  const [prompt, setPrompt] = useState("");
  const [selectedModel, setSelectedModel] = useState("flux-pro");
  const [imagePrompt, setImagePrompt] = useState<File | null>(null);
  const [settings, setSettings] = useState({
    width: 1024,
    height: 1024,
    count: 1,
    steps: 30,
    guidance: 7.5,
  });

  const models = [
    {
      id: "flux-schnell",
      name: "Flux Schnell",
      description: "快速生成基础图像",
    },
    { id: "flux-dev", name: "Flux Dev", description: "开发者友好的基础模型" },
    { id: "flux-pro", name: "Flux 1.1 Pro", description: "专业级图像生成" },
    {
      id: "flux-ultra",
      name: "Flux 1.1 Ultra",
      description: "超高质量图像生成",
    },
  ];

  const handleImagePromptChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setImagePrompt(e.target.files[0]);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setImagePrompt(e.dataTransfer.files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleGenerate = async () => {
    // TODO: 实现图像生成逻辑
    console.log("Generating with:", { prompt, selectedModel, settings, imagePrompt });
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 via-purple-950 to-indigo-950">
      <BackgroundBlurs />
      <div className="container relative z-10 mx-auto py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="grid gap-6 md:grid-cols-[2fr,1fr]"
        >
          {/* 左侧面板：提示词输入和生成结果 */}
          <div className="space-y-6">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card className="border-white/10 bg-white/5 shadow-xl backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white">提示词</CardTitle>
                  <CardDescription className="text-zinc-400">
                    描述您想要生成的图像内容
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div
                    className="mb-4 rounded-lg border-2 border-dashed border-white/10 p-4 text-center"
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                  >
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImagePromptChange}
                      className="hidden"
                      id="image-prompt"
                    />
                    <label
                      htmlFor="image-prompt"
                      className="cursor-pointer text-zinc-400 hover:text-white"
                    >
                      {imagePrompt ? (
                        <div className="relative h-40 w-full overflow-hidden rounded-lg">
                          <img
                            src={URL.createObjectURL(imagePrompt)}
                            alt="上传的图片"
                            className="h-full w-full object-cover"
                          />
                          <div className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity hover:opacity-100">
                            <p className="text-white">点击更换图片</p>
                          </div>
                        </div>
                      ) : (
                        <div className="py-8">
                          <p>拖拽图片到此处或点击上传</p>
                          <p className="mt-2 text-sm">支持 JPG、PNG 格式</p>
                        </div>
                      )}
                    </label>
                  </div>
                  <Textarea
                    placeholder="输入详细的图像描述..."
                    className="min-h-[200px] border-white/10 bg-white/5 text-white placeholder:text-zinc-500"
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                  />
                  <Button
                    className="mt-4 w-full bg-gradient-to-r from-violet-600 to-indigo-600 shadow-lg shadow-indigo-500/25 transition-all duration-300 hover:from-violet-500 hover:to-indigo-500"
                    size="lg"
                    onClick={handleGenerate}
                  >
                    生成图像
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Card className="border-white/10 bg-white/5 shadow-xl backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white">生成结果</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                    {/* TODO: 展示生成的图片 */}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* 右侧面板：模型选择和高级设置 */}
          <div className="space-y-6">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <Card className="border-white/10 bg-white/5 shadow-xl backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white">选择模型</CardTitle>
                  <CardDescription className="text-zinc-400">
                    选择适合您需求的 Flux 模型
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Select
                    value={selectedModel}
                    onValueChange={setSelectedModel}
                  >
                    <SelectTrigger className="border-white/10 bg-white/5 text-white">
                      <SelectValue>
                        {models.find(model => model.id === selectedModel)?.name}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent className="border-white/10 bg-indigo-950">
                      {models.map((model) => (
                        <SelectItem
                          key={model.id}
                          value={model.id}
                          className="text-white hover:bg-white/5"
                        >
                          <div>
                            <div className="font-semibold">{model.name}</div>
                            <div className="text-sm text-zinc-400">
                              {model.description}
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <Card className="border-white/10 bg-white/5 shadow-xl backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white">高级设置</CardTitle>
                </CardHeader>
                <CardContent>
                  <Accordion type="single" collapsible defaultValue="closed" className="w-full">
                    <AccordionItem
                      value="dimensions"
                      className="border-white/10"
                    >
                      <AccordionTrigger className="text-white hover:text-white/90">
                        图像尺寸
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="grid gap-4">
                          <div className="space-y-2">
                            <Label className="text-zinc-400">宽度</Label>
                            <Slider
                              min={512}
                              max={2048}
                              step={64}
                              value={[settings.width]}
                              onValueChange={([width]: [number]) =>
                                setSettings({ ...settings, width })
                              }
                              className="relative flex w-full touch-none select-none items-center"
                            />
                            <div className="text-right text-sm text-zinc-400">
                              {settings.width}px
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label className="text-zinc-400">高度</Label>
                            <Slider
                              min={512}
                              max={2048}
                              step={64}
                              value={[settings.height]}
                              onValueChange={([height]: [number]) =>
                                setSettings({ ...settings, height })
                              }
                              className="relative flex w-full touch-none select-none items-center"
                            />
                            <div className="text-right text-sm text-zinc-400">
                              {settings.height}px
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem
                      value="generation"
                      className="border-white/10"
                    >
                      <AccordionTrigger className="text-white hover:text-white/90">
                        生成参数
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="grid gap-4">
                          <div className="space-y-2">
                            <Label className="text-zinc-400">生成数量</Label>
                            <Select
                              value={settings.count.toString()}
                              onValueChange={(value) =>
                                setSettings({
                                  ...settings,
                                  count: parseInt(value),
                                })
                              }
                            >
                              <SelectTrigger className="border-white/10 bg-white/5 text-white">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent className="border-white/10 bg-indigo-950">
                                {[1, 2, 3, 4].map((num) => (
                                  <SelectItem
                                    key={num}
                                    value={num.toString()}
                                    className="text-white hover:bg-white/5"
                                  >
                                    {num} 张
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label className="text-zinc-400">采样步数</Label>
                            <Slider
                              min={20}
                              max={50}
                              step={1}
                              value={[settings.steps]}
                              onValueChange={([steps]: [number]) =>
                                setSettings({ ...settings, steps })
                              }
                              className="relative flex w-full touch-none select-none items-center"
                            />
                            <div className="text-right text-sm text-zinc-400">
                              {settings.steps} 步
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label className="text-zinc-400">引导系数</Label>
                            <Slider
                              min={1}
                              max={20}
                              step={0.1}
                              value={[settings.guidance]}
                              onValueChange={([guidance]: [number]) =>
                                setSettings({ ...settings, guidance })
                              }
                              className="relative flex w-full touch-none select-none items-center"
                            />
                            <div className="text-right text-sm text-zinc-400">
                              {settings.guidance}
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
