"use client";

import React, { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { Menu, X, User, LogIn } from "lucide-react";
import * as motion from "motion/react-client";
import { Button } from "@/components/ui/button";
import { LocaleSwitcher } from "@/components/LocaleSwitcher";
import { cn } from "@/lib/utils";
import { useTranslations } from 'next-intl';

interface NavbarProps {
  session: unknown;
}

export function Navbar({ session }: NavbarProps) {
  const t = useTranslations('navbar');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const handleScroll = useCallback(() => {
    setIsScrolled(window.scrollY > 150);
  }, []);

  useEffect(() => {
    if(window.scrollY > 150) {
      setIsScrolled(true);
    }

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  const closeMenu = useCallback(() => {
    setIsMenuOpen(false);
  }, []);

  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = "hidden";
      document.addEventListener("click", closeMenu);
    } else {
      document.body.style.overflow = "unset";
      document.removeEventListener("click", closeMenu);
    }
    return () => {
      document.body.style.overflow = "unset";
      document.removeEventListener("click", closeMenu);
    };
  }, [isMenuOpen, closeMenu]);

  // Helper function to check if session exists
  const isLoggedIn = !!session;

  return (
    <nav className="sticky top-0 z-50 flex w-full justify-center">
      <div
        className={cn(
          "transition-all duration-300",
          isScrolled
            ? "mx-auto my-2 w-auto rounded-full bg-indigo-950/60 px-4 shadow-lg backdrop-blur-sm"
            : "w-full",
        )}
      >
        <div
          className={cn(
            "px-4 py-4 sm:px-6 lg:px-8",
            isScrolled ? "max-w-fit" : "w-full",
          )}
        >
          <div
            className={cn(
              "flex items-center justify-between gap-8",
              isScrolled ? "justify-center" : "",
            )}
          >
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className={cn(
                "flex flex-1 items-center gap-2",
                isScrolled ? "hidden" : "",
              )}
            >
              <div className="text-2xl font-bold text-white">Flux Pix</div>
            </motion.div>

            <div className={cn("md:hidden", isScrolled ? "hidden" : "")}>
              <Button
                variant="ghost"
                size="icon"
                className="text-white"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsMenuOpen(!isMenuOpen);
                }}
                aria-label={isMenuOpen ? t('closeMenu') : t('openMenu')}
              >
                {isMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </Button>

              {isMenuOpen && (
                <>
                  <div
                    className="fixed inset-0 z-40 bg-black/60 backdrop-blur-sm"
                    onClick={closeMenu}
                  />
                  <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    className="fixed inset-0 z-50 flex items-start justify-center bg-indigo-950/95 pt-16 shadow-lg backdrop-blur-sm"
                    onClick={(e: React.MouseEvent) => e.stopPropagation()}
                  >
                    <div className="mx-4 flex w-full max-w-lg flex-col space-y-6">
                      <div className="flex items-center justify-between">
                        <h3 className="text-2xl font-bold text-white">
                          Flux Pix
                        </h3>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-white"
                          onClick={closeMenu}
                          aria-label={t('closeMenu')}
                        >
                          <X className="h-6 w-6" />
                        </Button>
                      </div>
                      <div className="flex flex-col space-y-4">
                        <Link
                          href="/generate"
                          className="flex items-center gap-2 py-2 text-white/80 transition-colors hover:text-white"
                        >
                          {t('generate')}
                        </Link>
                        <Link
                          href="/gallery"
                          className="flex items-center gap-2 py-2 text-white/80 transition-colors hover:text-white"
                        >
                          {t('gallery')}
                        </Link>
                        <Link
                          href="/pricing"
                          className="flex items-center gap-2 py-2 text-white/80 transition-colors hover:text-white"
                        >
                          {t('pricing')}
                        </Link>
                        {isLoggedIn && (
                          <Link
                            href="/profile"
                            className="flex items-center gap-2 py-2 text-white/80 transition-colors hover:text-white"
                          >
                            <User className="h-5 w-5" />
                            {t('profile')}
                          </Link>
                        )}
                        <div className="border-t border-white/10 pt-4">
                          <div className="flex flex-col space-y-4">
                            <div className="relative z-[100]">
                              <LocaleSwitcher />
                            </div>
                            <Button
                              variant="outline"
                              className="flex w-full items-center gap-2 border-indigo-400/50 bg-indigo-600/90 text-white shadow-lg shadow-indigo-500/20 hover:bg-indigo-500/90"
                              asChild
                            >
                              <a
                                href={
                                  isLoggedIn
                                    ? "/api/auth/signout"
                                    : "/api/auth/signin"
                                }
                              >
                                <LogIn className="h-5 w-5" />
                                {isLoggedIn ? t('signOut') : t('signInWithGoogle')}
                              </a>
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </>
              )}
            </div>

            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className={cn(
                "hidden items-center space-x-8 md:flex",
                isScrolled ? "mx-auto md:flex" : "",
              )}
            >
              <Link
                href="/generate"
                className="flex items-center gap-1 text-white/80 transition-colors hover:text-white"
              >
                {t('generate')}
              </Link>
              <Link
                href="/gallery"
                className="text-white/80 transition-colors hover:text-white"
              >
                {t('gallery')}
              </Link>
              <Link
                href="/pricing"
                className="text-white/80 transition-colors hover:text-white"
              >
                {t('pricing')}
              </Link>
              {isLoggedIn && (
                <Link
                  href="/profile"
                  className="flex items-center gap-1 text-white/80 transition-colors hover:text-white"
                >
                  <User className="h-4 w-4" />
                  {t('profile')}
                </Link>
              )}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className={cn(
                "hidden flex-1 items-center justify-end gap-4 md:flex",
                isScrolled ? "!hidden" : "",
              )}
            >
              <LocaleSwitcher />
              <Button
                variant="outline"
                className="flex items-center gap-2 border-indigo-400/50 bg-indigo-600/90 text-white shadow-lg shadow-indigo-500/20 hover:bg-indigo-500/90"
                asChild
              >
                <a href={isLoggedIn ? "/api/auth/signout" : "/api/auth/signin"}>
                  <LogIn className="h-4 w-4" />
                  {isLoggedIn ? t('signOut') : t('signInWithGoogle')}
                </a>
              </Button>
            </motion.div>
          </div>
        </div>
      </div>
    </nav>
  );
}
