"use client";

import { X, <PERSON><PERSON><PERSON> } from "lucide-react";
import * as motion from "motion/react-client";
import { useRef, useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { ScrollAnimation } from "@/components/ui/scroll-animation";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Masonry from "react-masonry-css";

interface ImagePreviewProps {
  url: string;
  onClose: () => void;
}

function ImagePreview({ url, onClose }: ImagePreviewProps) {
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    },
    [onClose],
  );

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);

  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/90"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="relative flex h-full max-h-[80vh] w-full max-w-5xl items-center justify-center"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ type: "spring", damping: 25 }}
        onClick={(e) => e.stopPropagation()}
      >
        <button
          className="absolute right-4 top-4 z-10 rounded-full bg-black/40 p-1 text-white transition-colors hover:bg-black/60"
          onClick={onClose}
        >
          <X className="h-6 w-6" />
        </button>

        {/* Image only */}
        <motion.div
          className="flex h-full w-full items-center justify-center p-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div
            className={`relative flex aspect-auto max-h-full items-center justify-center rounded-xl shadow-2xl`}
          >
            <Image
              fill
              alt=""
              src={url}
              className="object-contain"
              sizes="(max-width: 1200px) 100vw, 1200px"
            />
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}

export function GallerySection() {
  const t = useTranslations("gallery");
  const containerRef = useRef<HTMLDivElement>(null);
  const [previewItem, setPreviewItem] = useState<string | null>(null);

  const breakpointColumnsObj = {
    default: 4,
    1100: 3,
    700: 2,
    500: 1,
  };

  // Convert gallery items to photo objects
  const photos: string[] = [
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1267262452559642715_3.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/08/1268529580285759508_1.png",
    "https://blackforestlabs.ai/wp-content/uploads/2024/08/1268532719944601731_0.png",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1268208630935715891_1.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1263300321107775509_1.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/08/1268512566032859240_3.png",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1268030332691808257_2-1920x1079.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1257604452198912102_3.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1266008574673420371_0.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1266008183005249641_2.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1263822171168309323_1.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/08/1268537850182504511_2.png",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1268007375588298874_2.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1263276431341912235_2-1920x821.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1263970162713821184_1.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1265764484228845681_2.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1266348912130785303_0.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1263914871628763188_0.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1266361804049743885_3.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1257628583170408480_3.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1268267884082102375_4.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1263289155966865519_1.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1265955685338845235_3.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/07/1265781086697426957_2.-1.jpg",
    "https://blackforestlabs.ai/wp-content/uploads/2024/08/1268513229156388946_0.jpg",
  ];

  return (
    <section className="py-16">
      <ScrollAnimation direction="up" className="mb-16 text-center">
        <h2 className="mb-4 text-3xl font-bold text-white">
          {t("sectionTitle")}
        </h2>
        <p className="mx-auto max-w-3xl text-xl text-zinc-400">
          {t("sectionSubtitle")}
        </p>
      </ScrollAnimation>

      <div className="container mx-auto px-4">
        <Masonry
          breakpointCols={breakpointColumnsObj}
          className="-ml-4 flex w-auto"
          columnClassName="pl-4 bg-clip-padding"
        >
          {photos.map((photo, index) => (
            <motion.div
              key={index}
              className="mb-4 overflow-hidden rounded-xl"
              whileHover={{ scale: 1.03 }}
              onClick={() => setPreviewItem(photo)}
            >
              <div className="relative">
                <Image
                  width={800}
                  height={600}
                  src={photo}
                  alt=""
                  className="h-auto w-full transition-transform duration-300 hover:scale-110"
                  sizes="(max-width: 500px) 100vw, (max-width: 700px) 50vw, (max-width: 1100px) 33vw, 25vw"
                />
              </div>
            </motion.div>
          ))}
        </Masonry>
      </div>

      {previewItem && (
        <ImagePreview url={previewItem} onClose={() => setPreviewItem(null)} />
      )}

      <div className="text-center">
        <Button
          className="bg-gradient-to-r from-violet-600 to-indigo-600 shadow-lg shadow-indigo-500/25 transition-all duration-300 hover:from-violet-500 hover:to-indigo-500"
          size="lg"
        >
          {t("viewMoreButton")} <ArrowRight className="ml-2" />
        </Button>
      </div>
    </section>
  );
}
