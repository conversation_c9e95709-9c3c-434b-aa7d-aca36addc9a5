import {
  Crown,
  Wand2,
  Image,
  PenTool,
  <PERSON>,
  Star,
  Zap,
  <PERSON>,
  <PERSON>,
  <PERSON>,
} from "lucide-react";
import * as motion from "motion/react-client";
import { ScrollAnimation } from "@/components/ui/scroll-animation";
import { useTranslations } from 'next-intl';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  isPro?: boolean;
}

function FeatureCard({
  icon,
  title,
  description,
  isPro = false,
}: FeatureCardProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.03 }}
      className={`rounded-xl p-6 backdrop-blur-sm ${
        isPro
          ? "border border-violet-500/20 bg-gradient-to-br from-violet-500/10 to-indigo-600/10"
          : "bg-white/5"
      }`}
    >
      <div
        className={`rounded-lg p-3 ${isPro ? "bg-violet-500/20" : "bg-indigo-500/20"} mb-4 w-fit`}
      >
        {icon}
      </div>
      <h3 className="mb-2 flex items-center gap-2 text-xl font-bold text-white">
        {title}
        {isPro && (
          <span className="rounded-full bg-violet-500/20 px-2 py-0.5 text-xs font-medium text-violet-300">
            PRO
          </span>
        )}
      </h3>
      <p className="text-sm text-zinc-300">{description}</p>
    </motion.div>
  );
}

export function FeaturesSection() {
  const t = useTranslations('features');
  
  return (
    <section className="py-16">
      <ScrollAnimation direction="up" className="mb-16 text-center">
        <h2 className="mb-4 text-3xl font-bold text-white">
          {t('sectionTitle')}
        </h2>
        <p className="mx-auto max-w-3xl text-xl text-zinc-400">
          {t('sectionSubtitle')}
        </p>
      </ScrollAnimation>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
        <ScrollAnimation
          direction="up"
          delay={0.1}
          className="rounded-xl bg-white/5 p-6 backdrop-blur-sm"
        >
          <FeatureCard
            icon={<Brain className="h-6 w-6 text-indigo-400" />}
            title={t('smartPrompts.title')}
            description={t('smartPrompts.description')}
            isPro={true}
          />
        </ScrollAnimation>

        <ScrollAnimation
          direction="up"
          delay={0.2}
          className="rounded-xl bg-white/5 p-6 backdrop-blur-sm"
        >
          <FeatureCard
            icon={<Wand2 className="h-6 w-6 text-indigo-400" />}
            title={t('multipleModels.title')}
            description={t('multipleModels.description')}
          />
        </ScrollAnimation>

        <ScrollAnimation
          direction="up"
          delay={0.3}
          className="rounded-xl bg-white/5 p-6 backdrop-blur-sm"
        >
          <FeatureCard
            icon={<Image className="h-6 w-6 text-indigo-400" />}
            title={t('imageEditing.title')}
            description={t('imageEditing.description')}
            isPro={true}
          />
        </ScrollAnimation>

        <ScrollAnimation
          direction="up"
          delay={0.4}
          className="rounded-xl bg-white/5 p-6 backdrop-blur-sm"
        >
          <FeatureCard
            icon={<Zap className="h-6 w-6 text-indigo-400" />}
            title={t('fastGeneration.title')}
            description={t('fastGeneration.description')}
          />
        </ScrollAnimation>

        <ScrollAnimation
          direction="up"
          delay={0.5}
          className="rounded-xl bg-white/5 p-6 backdrop-blur-sm"
        >
          <FeatureCard
            icon={<Lock className="h-6 w-6 text-indigo-400" />}
            title={t('security.title')}
            description={t('security.description')}
          />
        </ScrollAnimation>

        <ScrollAnimation
          direction="up"
          delay={0.6}
          className="rounded-xl bg-white/5 p-6 backdrop-blur-sm"
        >
          <FeatureCard
            icon={<Users className="h-6 w-6 text-indigo-400" />}
            title={t('community.title')}
            description={t('community.description')}
          />
        </ScrollAnimation>
      </div>
    </section>
  );
}
