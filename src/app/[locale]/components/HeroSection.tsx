"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Zap, Image } from "lucide-react";
import * as motion from "motion/react-client";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollAnimation } from "@/components/ui/scroll-animation";
import { useEffect, useState } from "react";
import { useTranslations } from 'next-intl';

export function HeroSection() {
  const t = useTranslations('hero');
  const [typedText, setTypedText] = useState("");
  const fullText = t('typingText');
  const [typingComplete, setTypingComplete] = useState(false);
  const mainTextLength = t('typingMainPart').length;

  useEffect(() => {
    // 整合的打字效果
    let currentIndex = 0;
    const typingInterval = setInterval(() => {
      if (currentIndex <= fullText.length) {
        setTypedText(fullText.slice(0, currentIndex));
        currentIndex++;
      } else {
        clearInterval(typingInterval);
        setTypingComplete(true);
      }
    }, 100);

    return () => clearInterval(typingInterval);
  }, [fullText]);

  return (
    <div className="relative flex min-h-[calc(100vh-5rem)] flex-col items-center justify-center py-20">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="container relative z-10 mx-auto px-4 text-center sm:px-6 lg:px-8"
      >
        <h1 className="mx-auto max-w-4xl text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl">
          <span className="block text-gradient-purple">{t('title')}</span>
          <span className="mt-2 block">
            <span className="inline">{typedText.slice(0, mainTextLength)}</span>
            {typedText.length > mainTextLength && (
              <span className="inline bg-gradient-to-r from-violet-400 to-indigo-400 bg-clip-text text-transparent">
                {typedText.slice(mainTextLength)}
              </span>
            )}
            <span
              className={`ml-1 inline-block h-8 w-1 animate-blink bg-indigo-400 ${
                typingComplete ? "opacity-0" : "opacity-100"
              }`}
            ></span>
          </span>
        </h1>

        <p className="mx-auto mt-6 max-w-2xl text-center text-lg text-zinc-300 sm:text-xl">
          {t('subtitle')}
        </p>

        <div className="mx-auto mt-10 flex max-w-xs flex-col justify-center gap-4 sm:max-w-none sm:flex-row">
          <Button
            size="lg"
            className="bg-gradient-to-r from-violet-600 to-indigo-600 text-white shadow-lg shadow-indigo-500/25 transition-all duration-300 hover:from-violet-500 hover:to-indigo-500 hover:text-white hover:shadow-indigo-400/30"
          >
            {t('getStartedButton')} <ArrowRight className="ml-2" />
          </Button>

          <Button
            size="lg"
            variant="outline"
            className="border-violet-500/50 bg-transparent text-white hover:bg-violet-500/10"
          >
            {t('learnMoreButton')}
          </Button>
        </div>

        <div className="mt-16 grid grid-cols-1 gap-6 sm:grid-cols-3">
          <ScrollAnimation
            direction="up"
            delay={0.3}
            className="rounded-xl bg-white/5 p-4 backdrop-blur-sm"
          >
            <Sparkles className="mx-auto mb-3 h-8 w-8 text-violet-400" />
            <h3 className="mb-1 font-medium text-white">{t('features.aiGeneration.title')}</h3>
            <p className="text-sm text-zinc-400">
              {t('features.aiGeneration.description')}
            </p>
          </ScrollAnimation>

          <ScrollAnimation
            direction="up"
            delay={0.4}
            className="rounded-xl bg-white/5 p-4 backdrop-blur-sm"
          >
            <Zap className="mx-auto mb-3 h-8 w-8 text-violet-400" />
            <h3 className="mb-1 font-medium text-white">{t('features.smartPrompts.title')}</h3>
            <p className="text-sm text-zinc-400">
              {t('features.smartPrompts.description')}
            </p>
          </ScrollAnimation>

          <ScrollAnimation
            direction="up"
            delay={0.5}
            className="rounded-xl bg-white/5 p-4 backdrop-blur-sm"
          >
            <Image className="mx-auto mb-3 h-8 w-8 text-violet-400" />
            <h3 className="mb-1 font-medium text-white">{t('features.proEditing.title')}</h3>
            <p className="text-sm text-zinc-400">
              {t('features.proEditing.description')}
            </p>
          </ScrollAnimation>
        </div>
      </motion.div>
    </div>
  );
}
