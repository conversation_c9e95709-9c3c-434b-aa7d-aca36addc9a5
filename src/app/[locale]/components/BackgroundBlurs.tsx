"use client";

import { motion } from "motion/react";

export function BackgroundBlurs() {
  return (
    <div className="pointer-events-none absolute inset-0 overflow-hidden">
      <motion.div
        className="absolute -right-40 -top-40 h-80 w-80 rounded-full bg-violet-800/10 blur-3xl"
        animate={{
          y: ["0vh", "30vh"],
        }}
        transition={{
          repeat: Infinity,
          repeatType: "reverse",
          duration: 20,
          ease: "linear",
        }}
      />
      <motion.div
        className="absolute -left-20 top-1/3 h-60 w-60 rounded-full bg-indigo-800/10 blur-3xl"
        animate={{
          y: ["0vh", "-20vh"],
        }}
        transition={{
          repeat: Infinity,
          repeatType: "reverse",
          duration: 15,
          ease: "linear",
        }}
      />
      <motion.div
        className="absolute bottom-1/4 right-1/4 h-40 w-40 rounded-full bg-purple-800/10 blur-3xl"
        animate={{
          y: ["0vh", "10vh"],
        }}
        transition={{
          repeat: Infinity,
          repeatType: "reverse",
          duration: 25,
          ease: "linear",
        }}
      />
    </div>
  );
}
