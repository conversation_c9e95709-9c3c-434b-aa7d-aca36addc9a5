import { Check, X } from "lucide-react";
import * as motion from "motion/react-client";
import { Button } from "@/components/ui/button";
import { ScrollAnimation } from "@/components/ui/scroll-animation";
import { useTranslations } from 'next-intl';

interface PricingFeature {
  text: string;
  included: boolean;
}

interface PricingPlanProps {
  title: string;
  price: string;
  description: string;
  features: PricingFeature[];
  isPopular?: boolean;
  buttonText: string;
  credits: string;
  popularLabel: string;
}

function PricingPlan({
  title,
  price,
  description,
  features,
  isPopular = false,
  buttonText,
  credits,
  popularLabel,
}: PricingPlanProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className={`rounded-xl border p-6 backdrop-blur-sm ${
        isPopular
          ? "border-violet-500/30 bg-gradient-to-br from-violet-500/10 to-indigo-600/10"
          : "border-white/10 bg-white/5"
      } relative`}
    >
      {isPopular && (
        <div className="absolute -top-3 left-1/2 -translate-x-1/2 rounded-full bg-violet-500 px-3 py-1 text-xs font-medium text-white">
          {popularLabel}
        </div>
      )}

      <div className="mb-6 text-center">
        <h3 className="mb-2 text-xl font-bold text-white">{title}</h3>
        <div className="mb-2 text-3xl font-bold text-white">{price}</div>
        <p className="text-sm text-zinc-400">{description}</p>
      </div>

      <div className="mb-6">
        <div className="mb-4 rounded-lg bg-white/10 p-3 text-center">
          <span className="font-medium text-white">{credits}</span>
        </div>

        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center gap-2">
              {feature.included ? (
                <Check className="h-5 w-5 text-green-400" />
              ) : (
                <X className="h-5 w-5 text-red-400" />
              )}
              <span
                className={`text-sm ${feature.included ? "text-zinc-300" : "text-zinc-500"}`}
              >
                {feature.text}
              </span>
            </li>
          ))}
        </ul>
      </div>

      <Button
        className={`w-full ${
          isPopular
            ? "bg-violet-600 hover:bg-violet-700"
            : "bg-white/10 hover:bg-white/20"
        }`}
      >
        {buttonText}
      </Button>
    </motion.div>
  );
}

export function PricingSection() {
  const t = useTranslations('pricing');
  
  const freePlanFeatures = [
    { text: t('freePlan.features.dailyCredits'), included: true },
    { text: t('freePlan.features.trialPeriod'), included: true },
    { text: t('freePlan.features.resultStorage'), included: true },
    { text: t('freePlan.features.privateResults'), included: false },
    { text: t('freePlan.features.commercialUse'), included: false },
    { text: t('freePlan.features.advertisements'), included: false },
  ];

  const basicPlanFeatures = [
    { text: t('basicPlan.features.monthlyCredits'), included: true },
    { text: t('basicPlan.features.noAds'), included: true },
    { text: t('basicPlan.features.longTermStorage'), included: true },
    { text: t('basicPlan.features.privateResults'), included: true },
    { text: t('basicPlan.features.commercialUse'), included: true },
    { text: t('basicPlan.features.unlimitedPrompts'), included: false },
  ];

  const proPlanFeatures = [
    { text: t('proPlan.features.monthlyCredits'), included: true },
    { text: t('proPlan.features.unlimitedPrompts'), included: true },
    { text: t('proPlan.features.noAds'), included: true },
    { text: t('proPlan.features.longTermStorage'), included: true },
    { text: t('proPlan.features.privateResults'), included: true },
    { text: t('proPlan.features.commercialUse'), included: true },
  ];

  return (
    <section className="py-16">
      <ScrollAnimation direction="up" className="mb-16 text-center">
        <h2 className="mb-4 text-3xl font-bold text-white">
          {t('sectionTitle')}
        </h2>
        <p className="mx-auto max-w-3xl text-xl text-zinc-400">
          {t('sectionSubtitle')}
        </p>
      </ScrollAnimation>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
        <ScrollAnimation
          key="free"
          direction="up"
          delay={0.2}
          className={`relative overflow-hidden rounded-xl ${
            false
              ? "border-2 border-violet-500 bg-gradient-to-br from-violet-500/20 to-indigo-500/20"
              : "border border-violet-500/20 bg-white/5"
          } p-6 backdrop-blur-sm`}
        >
          <PricingPlan
            title={t('freePlan.title')}
            price={t('freePlan.price')}
            description={t('freePlan.description')}
            features={freePlanFeatures}
            buttonText={t('freePlan.buttonText')}
            credits={t('freePlan.credits')}
            popularLabel={t('popularLabel')}
          />
        </ScrollAnimation>

        <ScrollAnimation
          key="basic"
          direction="up"
          delay={0.3}
          className={`relative overflow-hidden rounded-xl ${
            true
              ? "border-2 border-violet-500 bg-gradient-to-br from-violet-500/20 to-indigo-500/20"
              : "border border-violet-500/20 bg-white/5"
          } p-6 backdrop-blur-sm`}
        >
          <PricingPlan
            title={t('basicPlan.title')}
            price={t('basicPlan.price')}
            description={t('basicPlan.description')}
            features={basicPlanFeatures}
            isPopular={true}
            buttonText={t('basicPlan.buttonText')}
            credits={t('basicPlan.credits')}
            popularLabel={t('popularLabel')}
          />
        </ScrollAnimation>

        <ScrollAnimation
          key="pro"
          direction="up"
          delay={0.4}
          className={`relative overflow-hidden rounded-xl ${
            false
              ? "border-2 border-violet-500 bg-gradient-to-br from-violet-500/20 to-indigo-500/20"
              : "border border-violet-500/20 bg-white/5"
          } p-6 backdrop-blur-sm`}
        >
          <PricingPlan
            title={t('proPlan.title')}
            price={t('proPlan.price')}
            description={t('proPlan.description')}
            features={proPlanFeatures}
            buttonText={t('proPlan.buttonText')}
            credits={t('proPlan.credits')}
            popularLabel={t('popularLabel')}
          />
        </ScrollAnimation>
      </div>
    </section>
  );
}
