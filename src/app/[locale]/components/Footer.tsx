import Link from "next/link";
import { useTranslations } from 'next-intl';

export function Footer() {
  const t = useTranslations('footer');
  
  return (
    <footer className="border-t border-white/10 bg-indigo-950/50 py-12">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
          <div>
            <h3 className="mb-4 text-lg font-semibold text-white">{t('product')}</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-sm text-zinc-400 hover:text-white">
                  {t('features')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sm text-zinc-400 hover:text-white">
                  {t('pricing')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sm text-zinc-400 hover:text-white">
                  {t('gallery')}
                </a>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="mb-4 text-lg font-semibold text-white">{t('resources')}</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-sm text-zinc-400 hover:text-white">
                  {t('documentation')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sm text-zinc-400 hover:text-white">
                  {t('tutorials')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sm text-zinc-400 hover:text-white">
                  {t('blog')}
                </a>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="mb-4 text-lg font-semibold text-white">{t('company')}</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-sm text-zinc-400 hover:text-white">
                  {t('about')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sm text-zinc-400 hover:text-white">
                  {t('careers')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sm text-zinc-400 hover:text-white">
                  {t('contact')}
                </a>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="mb-4 text-lg font-semibold text-white">{t('legal')}</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-sm text-zinc-400 hover:text-white">
                  {t('terms')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sm text-zinc-400 hover:text-white">
                  {t('privacy')}
                </a>
              </li>
              <li>
                <a href="#" className="text-sm text-zinc-400 hover:text-white">
                  {t('cookies')}
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div className="mt-12 border-t border-white/10 pt-8 text-center">
          <p className="text-sm text-zinc-400">
            &copy; {new Date().getFullYear()} Flux-Pix. {t('copyright')}
          </p>
        </div>
      </div>
    </footer>
  );
}
