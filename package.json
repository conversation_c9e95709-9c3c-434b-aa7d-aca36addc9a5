{"name": "app-template", "version": "0.1.0", "private": true, "type": "module", "scripts": {"check": "next lint && tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "dev": "next dev --turbo", "build": "next build", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/drizzle-adapter": "^1.7.2", "@formatjs/intl-localematcher": "^0.6.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-query": "^5.50.0", "@trpc/client": "^11.0.0-rc.446", "@trpc/react-query": "^11.0.0-rc.446", "@trpc/server": "^11.0.0-rc.446", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.33.0", "geist": "^1.3.0", "lucide-react": "^0.475.0", "motion": "^12.4.10", "negotiator": "^1.0.0", "next": "^15.0.1", "next-auth": "5.0.0-beta.25", "next-intl": "^3.26.4", "postgres": "^3.4.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-masonry-css": "^1.0.16", "server-only": "^0.0.1", "superjson": "^2.2.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.3"}, "devDependencies": {"@types/eslint": "^8.56.10", "@types/negotiator": "^0.6.3", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "drizzle-kit": "^0.24.0", "eslint": "^8.57.0", "eslint-config-next": "^15.0.1", "eslint-plugin-drizzle": "^0.2.3", "postcss": "^8.4.39", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.3", "typescript": "^5.5.3"}, "ct3aMetadata": {"initVersion": "7.38.1"}, "packageManager": "pnpm@8.15.0"}